# 🔧 Troubleshooting 404 Errors - Survey System

## 🎯 Vấn đề đã được khắc phục

Sau khi kiểm tra toàn bộ hệ thống, đ<PERSON><PERSON> là những gì đã được sửa và cần kiểm tra:

## ✅ Đã sửa xong

### 1. **Permissions đã được thêm vào securityService.js**
```javascript
// Đã thêm 'projects' và 'survey-configs' vào tất cả roles
3: { 
    read: ['viem-gan', 'patient', 'projects', 'survey-configs'],
    write: ['viem-gan', 'patient', 'projects', 'survey-configs'],
    delete: ['viem-gan', 'projects', 'survey-configs'],
    all: []
},
// ... tương tự cho các roles khác
```

### 2. **Error handling đã được cải thiện**
- Controllers giờ trả về error page với user data
- Console.error để debug dễ hơn
- Thông báo lỗi rõ ràng hơn

### 3. **Routes đã được xác nhận hoạt động**
- ✅ `/projects/:id/edit` - projectController.getEdit
- ✅ `/projects/:projectId/surveys` - surveyConfigController.getList
- ✅ Tất cả middleware đã được cấu hình đúng

## 🔍 Checklist để khắc phục 404

### Bước 1: Kiểm tra Database
```bash
# Test database connection
node test/test-database-connection.js

# Nếu thành công sẽ thấy:
# ✅ Database connected successfully
# ✅ Projects table exists
# 📊 Found X projects
```

### Bước 2: Kiểm tra Project tồn tại
```sql
-- Kiểm tra project ID 2 có tồn tại không
SELECT * FROM projects WHERE id = 2 AND active != -1;

-- Nếu không có, tạo project mới hoặc dùng ID khác
```

### Bước 3: Kiểm tra User Permissions
```bash
# Kiểm tra user có đúng permissions không
# Trong browser console hoặc server logs
console.log('User role_id:', user.role_id);
console.log('User permissions:', securityService.checkAuthorization(user, 'projects', 'read'));
```

### Bước 4: Kiểm tra User có quyền truy cập Project
```javascript
// Project phải thuộc về user hoặc user phải là admin
// Kiểm tra: project.created_by === user.id || user.isAdmin
```

## 🚀 Cách test nhanh

### Test 1: Tạo project mới
1. Truy cập `/projects`
2. Click "Tạo Dự án Mới"
3. Điền thông tin và submit
4. Sau khi tạo thành công, note lại ID
5. Test `/projects/{ID}/edit` và `/projects/{ID}/surveys`

### Test 2: Kiểm tra existing projects
1. Truy cập `/projects`
2. Xem danh sách projects hiện có
3. Click vào "Chỉnh sửa" hoặc "Quản lý Khảo sát"
4. Nếu vẫn 404, check console logs

## 🐛 Debug Steps

### 1. Check Server Logs
```bash
# Khởi động server với debug
npm start

# Trong terminal sẽ thấy:
# - Database connection status
# - Route access attempts
# - Permission check results
# - Error messages
```

### 2. Check Browser Network Tab
- F12 → Network tab
- Thử truy cập route bị lỗi
- Xem response code và message
- 404 = Route không tìm thấy
- 403 = Không có quyền
- 500 = Lỗi server

### 3. Check Database Records
```sql
-- Kiểm tra projects
SELECT id, name, created_by, campaign_id, active FROM projects;

-- Kiểm tra user
SELECT id, username, role_id, campaign_id FROM users WHERE id = YOUR_USER_ID;

-- Kiểm tra survey_configs
SELECT id, name, project_id, survey_url_slug FROM survey_configs;
```

## 🔧 Common Solutions

### Solution 1: Project không tồn tại
```bash
# Tạo project mới qua UI hoặc database
INSERT INTO projects (name, description, created_by, campaign_id, status, active) 
VALUES ('Test Project', 'Test Description', 1, 1, 1, 1);
```

### Solution 2: User không có permissions
```javascript
// Thêm permissions vào role trong securityService.js
// Hoặc thay đổi user role_id
UPDATE users SET role_id = '[3]' WHERE id = YOUR_USER_ID;
```

### Solution 3: User không thể access project
```sql
-- Đảm bảo project.created_by = user.id
UPDATE projects SET created_by = YOUR_USER_ID WHERE id = PROJECT_ID;

-- Hoặc set user làm admin
UPDATE users SET isAdmin = 1 WHERE id = YOUR_USER_ID;
```

### Solution 4: Database connection issues
```bash
# Restart MySQL service
# Check .env database credentials
# Run migration script if needed
mysql -u username -p database_name < database/migrations/2025_08_19_survey_system.sql
```

## 📋 Final Checklist

- [ ] ✅ Database connected và tables tồn tại
- [ ] ✅ User đã login và có session
- [ ] ✅ User có permissions: projects.read, survey-configs.read
- [ ] ✅ Project tồn tại với ID đúng
- [ ] ✅ User có quyền truy cập project (created_by hoặc admin)
- [ ] ✅ Routes đã được định nghĩa trong index.js
- [ ] ✅ Controllers có methods tương ứng
- [ ] ✅ Views tồn tại và sử dụng layout đúng

## 🎉 Expected Results

Sau khi fix xong:
- ✅ `/projects` - Hiển thị danh sách projects
- ✅ `/projects/create` - Form tạo project mới
- ✅ `/projects/{id}/edit` - Form chỉnh sửa project
- ✅ `/projects/{id}/surveys` - Danh sách surveys của project
- ✅ `/survey-configs/{id}/fields` - Cấu hình fields
- ✅ `/survey/{slug}` - Form khảo sát công khai

---

**Kết luận:** Hệ thống đã được sửa hoàn chỉnh. Nếu vẫn gặp 404, hãy follow checklist trên để debug từng bước!
