// Test logic order parsing
console.log('=== Testing Order Logic ===\n');

// Simulate the order processing logic from getDataTableData
function testOrderProcessing() {
    // Test data giống như trong patientController
    const orders = [
        { column: 'khan_cap', dir: 'DESC' },
        { column: 'ngay_hoi_chan', dir: 'DESC' },
        { column: 'id', dir: 'DESC' }
    ];
    
    console.log('Input orders:', orders);
    
    // Logic từ getDataTableData (đã sửa)
    const orderParts = orders.map(order => {
        if (!order.column || !order.dir) {
            throw new Error('Order object must have column and dir properties');
        }
        
        let columnName;
        
        // Kiểm tra xem order.column là index hay column name
        if (typeof order.column === 'number' || !isNaN(parseInt(order.column))) {
            console.log(`Column "${order.column}" is treated as index`);
            columnName = 'id'; // fallback
        } else {
            console.log(`Column "${order.column}" is treated as column name`);
            columnName = order.column;
        }

        const direction = order.dir.toUpperCase() === 'DESC' ? 'DESC' : 'ASC';
        const result = `${columnName} ${direction}`;
        console.log(`Order part: ${result}`);
        return result;
    });
    
    const orderClause = ` ORDER BY ${orderParts.join(', ')}`;
    console.log('\nFinal ORDER clause:', orderClause);
    
    // Expected: ORDER BY khan_cap DESC, ngay_hoi_chan DESC, id DESC
    const expected = ' ORDER BY khan_cap DESC, ngay_hoi_chan DESC, id DESC';
    
    if (orderClause === expected) {
        console.log('✅ Test PASSED - Order clause matches expected result');
    } else {
        console.log('❌ Test FAILED');
        console.log('Expected:', expected);
        console.log('Actual  :', orderClause);
    }
}

// Test với column index (old behavior)
function testWithColumnIndex() {
    console.log('\n=== Testing with Column Index (old behavior) ===');
    
    const orders = [
        { column: '1', dir: 'DESC' },
        { column: 2, dir: 'ASC' }
    ];
    
    const columns = ['id', 'fullname', 'phone', 'email'];
    
    console.log('Input orders:', orders);
    console.log('Available columns:', columns);
    
    const orderParts = orders.map(order => {
        let columnName;
        
        if (typeof order.column === 'number' || !isNaN(parseInt(order.column))) {
            const columnIdx = parseInt(order.column);
            console.log(`Column index: ${columnIdx}`);
            
            if (columnIdx >= 0 && columnIdx < columns.length) {
                columnName = columns[columnIdx];
                console.log(`Mapped to column: ${columnName}`);
            } else {
                columnName = 'id'; // fallback
                console.log(`Index out of range, fallback to: ${columnName}`);
            }
        } else {
            columnName = order.column;
            console.log(`Direct column name: ${columnName}`);
        }

        const direction = order.dir.toUpperCase() === 'DESC' ? 'DESC' : 'ASC';
        return `${columnName} ${direction}`;
    });
    
    const orderClause = ` ORDER BY ${orderParts.join(', ')}`;
    console.log('Final ORDER clause:', orderClause);
    
    // Expected: ORDER BY fullname DESC, phone ASC
    const expected = ' ORDER BY fullname DESC, phone ASC';
    
    if (orderClause === expected) {
        console.log('✅ Test PASSED - Column index mapping works');
    } else {
        console.log('❌ Test FAILED');
        console.log('Expected:', expected);
        console.log('Actual  :', orderClause);
    }
}

// Run tests
testOrderProcessing();
testWithColumnIndex();

console.log('\n=== Tests completed ===');
