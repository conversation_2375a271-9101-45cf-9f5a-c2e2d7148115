/**
 * Test Database Connection
 * Ki<PERSON>m tra kết nối database và tables
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

async function testDatabaseConnection() {
    console.log('🔍 Testing Database Connection...\n');

    try {
        // Initialize database connection first
        console.log('0. Initializing database connection...');
        const db = require('../config/db');
        await new Promise((resolve, reject) => {
            db.connect('development', (err) => {
                if (err) {
                    console.log('   ❌ Database connection failed:', err.message);
                    reject(err);
                } else {
                    console.log('   ✅ Database connected successfully');
                    resolve();
                }
            });
        });

        const commonService = require('../services/commonService');
        
        console.log('1. Testing basic database connection...');
        
        // Test simple query
        try {
            const result = await commonService.getAllDataTable('projects', { active: [0, 1] });
            console.log(`   ✅ Database connected successfully`);
            console.log(`   ✅ Projects table exists`);
            console.log(`   📊 Found ${result.data ? result.data.length : 0} projects`);
            
            if (result.data && result.data.length > 0) {
                const project = result.data[0];
                console.log(`   📝 Sample project: ${project.name} (ID: ${project.id})`);
                console.log(`   👤 Created by: ${project.created_by}`);
                console.log(`   🏢 Campaign: ${project.campaign_id}`);
            }
            
        } catch (dbError) {
            console.log(`   ❌ Database error: ${dbError.message}`);
            
            if (dbError.message.includes('ER_NO_SUCH_TABLE')) {
                console.log('   💡 Solution: Run migration script');
                console.log('      mysql -u username -p database_name < database/migrations/2025_08_19_survey_system.sql');
            } else if (dbError.message.includes('ECONNREFUSED')) {
                console.log('   💡 Solution: Start MySQL server');
            } else if (dbError.message.includes('ER_ACCESS_DENIED')) {
                console.log('   💡 Solution: Check database credentials in .env file');
            }
            
            return false;
        }
        
        console.log('\n2. Testing survey_configs table...');
        
        try {
            const surveyResult = await commonService.getAllDataTable('survey_configs', { active: [0, 1] });
            console.log(`   ✅ survey_configs table exists`);
            console.log(`   📊 Found ${surveyResult.data ? surveyResult.data.length : 0} survey configs`);
        } catch (error) {
            console.log(`   ❌ survey_configs table error: ${error.message}`);
        }
        
        console.log('\n3. Testing survey_fields table...');
        
        try {
            const fieldsResult = await commonService.getAllDataTable('survey_fields', { active: 1 });
            console.log(`   ✅ survey_fields table exists`);
            console.log(`   📊 Found ${fieldsResult.data ? fieldsResult.data.length : 0} survey fields`);
        } catch (error) {
            console.log(`   ❌ survey_fields table error: ${error.message}`);
        }
        
        console.log('\n4. Testing specific project access...');
        
        try {
            const projectResult = await commonService.getAllDataTable('projects', { 
                id: 2,
                active: [0, 1] 
            });
            
            if (projectResult.success && projectResult.data && projectResult.data.length > 0) {
                const project = projectResult.data[0];
                console.log(`   ✅ Project ID 2 found: ${project.name}`);
                console.log(`   👤 Created by: ${project.created_by}`);
                console.log(`   📅 Created at: ${project.created_at}`);
                console.log(`   🔄 Status: ${project.status}`);
                
                // Test user access
                const testUser = {
                    id: project.created_by, // Same as creator
                    campaign_id: project.campaign_id,
                    role_id: [3],
                    isAdmin: false
                };
                
                const securityService = require('../services/securityService');
                const canAccess = securityService.canAccessRecord(testUser, project);
                console.log(`   🔐 User can access: ${canAccess}`);
                
            } else {
                console.log(`   ❌ Project ID 2 not found`);
                console.log(`   💡 This explains the 404 error`);
                console.log(`   💡 Create a project first or use existing project ID`);
            }
        } catch (error) {
            console.log(`   ❌ Error checking project 2: ${error.message}`);
        }
        
        return true;
        
    } catch (error) {
        console.log('❌ Database test failed:', error.message);
        return false;
    }
}

async function testUserAuthentication() {
    console.log('\n🔍 Testing User Authentication...\n');
    
    try {
        const commonService = require('../services/commonService');
        
        console.log('1. Testing user table...');
        
        try {
            const userResult = await commonService.getAllDataTable('users', { active: 1 });
            console.log(`   ✅ users table exists`);
            console.log(`   👥 Found ${userResult.data ? userResult.data.length : 0} users`);
            
            if (userResult.data && userResult.data.length > 0) {
                const user = userResult.data[0];
                console.log(`   👤 Sample user: ${user.username || user.email} (ID: ${user.id})`);
                console.log(`   🏢 Campaign: ${user.campaign_id}`);
                console.log(`   🔑 Role: ${user.role_id}`);
            }
            
        } catch (error) {
            console.log(`   ❌ users table error: ${error.message}`);
        }
        
    } catch (error) {
        console.log('❌ User authentication test failed:', error.message);
    }
}

async function runDatabaseTests() {
    console.log('🚀 Starting Database Tests\n');
    console.log('=' .repeat(50));
    
    const dbConnected = await testDatabaseConnection();
    
    if (dbConnected) {
        console.log('\n' + '=' .repeat(50));
        await testUserAuthentication();
    }
    
    console.log('\n' + '=' .repeat(50));
    console.log('📋 Database Test Summary:');
    
    if (dbConnected) {
        console.log('✅ Database connection working');
        console.log('✅ Survey system tables exist');
        console.log('💡 If still getting 404 errors:');
        console.log('   1. Check project ID exists');
        console.log('   2. Check user is logged in');
        console.log('   3. Check user has permissions');
        console.log('   4. Check user can access the project');
    } else {
        console.log('❌ Database connection failed');
        console.log('💡 Fix database connection first:');
        console.log('   1. Start MySQL server');
        console.log('   2. Check .env database credentials');
        console.log('   3. Run migration script');
        console.log('   4. Test connection again');
    }
    
    console.log('\n✅ Database tests completed!');
}

// Chạy tests
if (require.main === module) {
    runDatabaseTests().catch(console.error);
}

module.exports = {
    testDatabaseConnection,
    testUserAuthentication
};
