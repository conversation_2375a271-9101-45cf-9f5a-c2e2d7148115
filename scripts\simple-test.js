const fs = require('fs');
const path = require('path');

console.log('🚀 Testing responsive table setup...');

// Test 1: Check CSS file exists
const cssPath = path.join(__dirname, '../public/css/table-config.css');
if (fs.existsSync(cssPath)) {
    console.log('✅ CSS file exists: table-config.css');
    const cssContent = fs.readFileSync(cssPath, 'utf8');
    if (cssContent.includes('@media (max-width: 768px)')) {
        console.log('✅ Mobile responsive CSS found');
    } else {
        console.log('❌ Mobile responsive CSS not found');
    }
} else {
    console.log('❌ CSS file not found');
}

// Test 2: Check JS file exists
const jsPath = path.join(__dirname, '../public/js/responsive-table.js');
if (fs.existsSync(jsPath)) {
    console.log('✅ JS file exists: responsive-table.js');
} else {
    console.log('❌ JS file not found');
}

// Test 3: Check footer includes JS
const footerPath = path.join(__dirname, '../views/layout/footer.ejs');
if (fs.existsSync(footerPath)) {
    const footerContent = fs.readFileSync(footerPath, 'utf8');
    if (footerContent.includes('responsive-table.js')) {
        console.log('✅ Footer includes responsive-table.js');
    } else {
        console.log('❌ Footer does not include responsive-table.js');
    }
} else {
    console.log('❌ Footer file not found');
}

// Test 4: Find sample views with tables
const viewsDir = path.join(__dirname, '../views');
const sampleViews = [
    'patient/list.ejs',
    'research/listPatient.ejs',
    'admin/user/list.ejs'
];

console.log('\n📄 Checking sample views:');
sampleViews.forEach(viewPath => {
    const fullPath = path.join(viewsDir, viewPath);
    if (fs.existsSync(fullPath)) {
        const content = fs.readFileSync(fullPath, 'utf8');
        const hasTable = content.includes('<table');
        const hasResponsive = content.includes('table-responsive');
        const hasDataTable = content.includes('DataTable');
        
        console.log(`   ${viewPath}:`);
        console.log(`     Table: ${hasTable ? '✅' : '❌'}`);
        console.log(`     Responsive: ${hasResponsive ? '✅' : '❌'}`);
        console.log(`     DataTable: ${hasDataTable ? '✅' : '❌'}`);
    } else {
        console.log(`   ${viewPath}: ❌ Not found`);
    }
});

console.log('\n🎯 Setup complete! Ready to test responsive tables.');
console.log('📱 To test: Open any table view and resize browser to mobile width (<768px)');
