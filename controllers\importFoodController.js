var commonService = require('../services/commonService');
const securityService = require('../services/securityService');

let importFood = {
    /**
     * API to import food data from an Excel file.
     * Expected request body:
     * {
     *   rows: [               // array of arrays (as read by readXlsxFile)
     *     [header...],        // row 0 – column names
     *     [units...],         // row 1 – units (ignored)
     *     [dataRow1...],
     *     [dataRow2...],
     *     ...
     *   ]
     * }
     */
    importFromExcel: async function (req, res) {
        const result = { success: false, message: '', data: null };
        const user = req.user;

        // Permission check – only admins or role 6 can import
        if (!user || (!user.isAdmin && !user.role_id.includes(6))) {
            result.message = 'Bạn không có quyền thực hiện chức năng này!';
            return res.json(result);
        }

        const { rows, type_year, type } = req.body;
        if (!Array.isArray(rows)) {
            result.message = 'Dữ liệu không hợp lệ hoặc thiếu dữ liệu!';
            return res.json(result);
        }

        // Check for duplicates by name before inserting
        const existingFoods = await commonService.getAllDataTable('food_info', { 
            type: type, 
            type_year: type_year,
            active: 1
        });
        
        if (!existingFoods.success) {
            result.message = 'Lỗi khi kiểm tra dữ liệu trùng lặp!';
            return res.json(result);
        }
        
        const existingNames = existingFoods.data.map(food => food.name);
        const uniqueRecords = rows.filter(record => !existingNames.includes(record.name));
        
        if (uniqueRecords.length === 0) {
            result.message = 'Không có bản ghi mới để thêm (tất cả đều bị trùng)!';
            return res.json(result);
        }
        
        // Insert records into food_info table
        const insertResult = await commonService.addMutilRecordTable(uniqueRecords, 'food_info', true);
        if (!insertResult.success) {
            result.message = 'Lỗi khi lưu dữ liệu: ' + insertResult.message;
            return res.json(result);
        }
        
        result.success = true;
        result.message = `Đã import thành công ${uniqueRecords.length} thực phẩm!`;
        result.data = insertResult.data;
        return res.json(result);
    }
};

module.exports = importFood;
