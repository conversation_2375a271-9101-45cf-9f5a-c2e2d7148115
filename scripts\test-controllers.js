// Script để test các controllers đã được sửa
const fs = require('fs');

function testControllerSyntax(filePath) {
    console.log(`\n🧪 Testing ${filePath}...`);
    
    try {
        // Test syntax bằng cách require file
        delete require.cache[require.resolve(`../${filePath}`)];
        const controller = require(`../${filePath}`);
        
        console.log(`✅ ${filePath} - Syntax OK`);
        
        // Kiểm tra các method quan trọng
        const methods = Object.keys(controller);
        console.log(`📋 Methods found: ${methods.length}`);
        
        // Tìm các method có chứa DataTable
        const dataTableMethods = methods.filter(method => {
            const methodStr = controller[method].toString();
            return methodStr.includes('dataTableService') || methodStr.includes('getDataTableData');
        });
        
        if (dataTableMethods.length > 0) {
            console.log(`🔍 DataTable methods: ${dataTableMethods.join(', ')}`);
        }
        
        return true;
    } catch (error) {
        console.error(`❌ ${filePath} - Error:`, error.message);
        return false;
    }
}

function testAllControllers() {
    console.log('🚀 Testing all controllers...\n');
    
    const controllers = [
        'controllers/patientController.js',
        'controllers/researchController.js', 
        'controllers/adminController.js',
        'controllers/dishController.js',
        'controllers/hepatitisController.js',
        'controllers/tetanusController.js',
        'controllers/liverSurgeryController.js'
    ];
    
    let passCount = 0;
    let failCount = 0;
    
    controllers.forEach(controller => {
        if (fs.existsSync(controller)) {
            if (testControllerSyntax(controller)) {
                passCount++;
            } else {
                failCount++;
            }
        } else {
            console.log(`⚠️  File not found: ${controller}`);
            failCount++;
        }
    });
    
    console.log('\n' + '='.repeat(50));
    console.log('📊 TEST RESULTS');
    console.log('='.repeat(50));
    console.log(`✅ Passed: ${passCount}`);
    console.log(`❌ Failed: ${failCount}`);
    console.log(`📈 Success Rate: ${Math.round((passCount / (passCount + failCount)) * 100)}%`);
    
    if (failCount === 0) {
        console.log('\n🎉 All controllers passed syntax test!');
        console.log('\n📝 Next steps:');
        console.log('1. Start the server');
        console.log('2. Test each DataTable page');
        console.log('3. Verify sorting and searching works');
        console.log('4. Check for any runtime errors');
    } else {
        console.log('\n⚠️  Some controllers have issues. Please fix them before testing.');
    }
}

// Chạy test
if (require.main === module) {
    testAllControllers();
}

module.exports = { testControllerSyntax, testAllControllers };
