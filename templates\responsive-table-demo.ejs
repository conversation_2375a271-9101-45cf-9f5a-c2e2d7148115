<!DOCTYPE html>
<html lang="vi">
<head>
    <%- include('../views/layout/head') %>
    <title>Demo Responsive Table</title>
</head>
<body id="page-top">
    <div id="wrapper">
        <!-- Sidebar -->
        <%- include('../views/layout/sidebar') %>
        
        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">
            <!-- Main Content -->
            <div id="content">
                <!-- Topbar -->
                <%- include('../views/layout/topbar') %>
                
                <!-- Begin Page Content -->
                <div class="container-fluid">
                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">Demo Responsive Table</h1>
                    </div>
                    
                    <!-- DataTables Example -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary"><PERSON>h sách bệnh nhân (Demo)</h6>
                            <button class="btn btn-success btn-circle">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered" id="demoTable" width="100%" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>Thao tác</th>
                                            <th>Họ tên</th>
                                            <th>Số điện thoại</th>
                                            <th>Số phòng</th>
                                            <th>Chẩn đoán</th>
                                            <th>Ngày khám</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <div class="d-flex gap-2">
                                                    <a class="btn btn-info btn-sm btn-circle" href="#" title="Sửa">
                                                        <i class="fas fa-pen-square"></i>
                                                    </a>
                                                    <button class="btn btn-danger btn-sm btn-circle" title="Xóa">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                            <td>Nguyễn Văn A</td>
                                            <td><a href="tel:0123456789">0123456789</a></td>
                                            <td>101</td>
                                            <td>Viêm gan B</td>
                                            <td>15/03/2024</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex gap-2">
                                                    <a class="btn btn-info btn-sm btn-circle" href="#" title="Sửa">
                                                        <i class="fas fa-pen-square"></i>
                                                    </a>
                                                    <button class="btn btn-danger btn-sm btn-circle" title="Xóa">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                            <td>Trần Thị B</td>
                                            <td><a href="tel:0987654321">0987654321</a></td>
                                            <td>102</td>
                                            <td>Ung thư gan</td>
                                            <td>16/03/2024</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex gap-2">
                                                    <a class="btn btn-info btn-sm btn-circle" href="#" title="Sửa">
                                                        <i class="fas fa-pen-square"></i>
                                                    </a>
                                                    <button class="btn btn-danger btn-sm btn-circle" title="Xóa">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                            <td>Lê Văn C</td>
                                            <td><a href="tel:0369852147">0369852147</a></td>
                                            <td>103</td>
                                            <td>Xơ gan</td>
                                            <td>17/03/2024</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Instructions -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Hướng dẫn sử dụng</h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h5><i class="fas fa-info-circle"></i> Responsive Table</h5>
                                <p>Bảng này sẽ tự động chuyển thành layout card khi xem trên mobile (màn hình < 768px):</p>
                                <ul>
                                    <li>Trên desktop: Hiển thị dạng bảng thông thường</li>
                                    <li>Trên mobile: Mỗi row sẽ thành một card với các thông tin được sắp xếp dọc</li>
                                    <li>Mỗi cell sẽ có label tương ứng với header của column</li>
                                    <li>Buttons action sẽ được căn giữa và có spacing phù hợp</li>
                                </ul>
                            </div>
                            
                            <div class="alert alert-success">
                                <h5><i class="fas fa-check-circle"></i> Cách test</h5>
                                <p>Để test responsive design:</p>
                                <ol>
                                    <li>Mở Developer Tools (F12)</li>
                                    <li>Chuyển sang chế độ mobile hoặc thu nhỏ cửa sổ browser</li>
                                    <li>Xem bảng chuyển thành dạng card</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /.container-fluid -->
            </div>
            <!-- End of Main Content -->
            
            <!-- Footer -->
            <%- include('../views/layout/footer') %>
        </div>
        <!-- End of Content Wrapper -->
    </div>
    <!-- End of Page Wrapper -->

    <script>
        $(document).ready(function() {
            // Initialize DataTable for demo
            $('#demoTable').DataTable({
                responsive: true,
                pageLength: 10,
                lengthMenu: [10, 25, 50, 100],
                language: {
                    "sProcessing": "Đang xử lý...",
                    "sLengthMenu": "Xem _MENU_ mục",
                    "sZeroRecords": "Không tìm thấy dòng nào phù hợp",
                    "sInfo": "Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục",
                    "sInfoEmpty": "Đang xem 0 đến 0 trong tổng số 0 mục",
                    "sInfoFiltered": "(được lọc từ _MAX_ mục)",
                    "sInfoPostFix": "",
                    "sSearch": "Tìm:",
                    "sUrl": "",
                    "oPaginate": {
                        "sFirst": "Đầu",
                        "sPrevious": "Trước",
                        "sNext": "Tiếp",
                        "sLast": "Cuối"
                    }
                }
            });
        });
    </script>
</body>
</html>
