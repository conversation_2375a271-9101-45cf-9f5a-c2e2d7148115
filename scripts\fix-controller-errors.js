// Script để sửa lỗi trong các controllers
const fs = require('fs');
const path = require('path');

const controllersToFix = [
    'controllers/adminController.js',
    'controllers/researchController.js',
    'controllers/patientController.js'
];

function fixControllerErrors(filePath) {
    console.log(`\n🔧 Fixing errors in ${filePath}...`);
    
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        let modified = false;

        // Fix 1: Sửa lỗi dataTableService.createErrorResponse syntax
        const errorResponsePattern = /return res\.json\(dataTableService\.createErrorResponse\(req\.body,\s*\n\s*draw:\s*req\.body\.draw[^}]*\}\s*\)\s*\);/g;
        if (errorResponsePattern.test(content)) {
            content = content.replace(errorResponsePattern, 
                "return res.json(dataTableService.createErrorResponse(req.body, '<PERSON>ạn không có quyền truy cập danh sách này!'));"
            );
            modified = true;
            console.log('✅ Fixed createErrorResponse syntax');
        }

        // Fix 2: Sửa lỗi indentation và duplicate code
        const duplicateErrorPattern = /\}\s*catch\s*\([^}]*\}\s*\)\s*;\s*\}\s*catch/g;
        if (duplicateErrorPattern.test(content)) {
            content = content.replace(duplicateErrorPattern, '} catch');
            modified = true;
            console.log('✅ Fixed duplicate error handling');
        }

        // Fix 3: Sửa lỗi missing require statement position
        const misplacedRequirePattern = /(\s+)const dataTableService = require\('\.\.\/services\/dataTableService'\);\s*\n\s*\/\/ Cấu hình DataTable/g;
        if (misplacedRequirePattern.test(content)) {
            content = content.replace(misplacedRequirePattern, 
                '$1const dataTableService = require(\'../services/dataTableService\');\n$1\n$1// Cấu hình DataTable'
            );
            modified = true;
            console.log('✅ Fixed require statement position');
        }

        // Fix 4: Sửa lỗi indentation cho config object
        const configIndentPattern = /(\s+)const dataTableService = require[^;]*;\s*\n\s*\/\/ Cấu hình DataTable\s*\n\s*const config = \{/g;
        if (configIndentPattern.test(content)) {
            content = content.replace(configIndentPattern, 
                '$1const dataTableService = require(\'../services/dataTableService\');\n$1\n$1// Cấu hình DataTable\n$1const config = {'
            );
            modified = true;
            console.log('✅ Fixed config indentation');
        }

        // Fix 5: Sửa lỗi trailing code sau dataTableService.handleDataTableRequest
        const trailingCodePattern = /(dataTableService\.handleDataTableRequest\(req, res, config[^;]*\);)\s*[^}]*\}\s*\)\s*;/g;
        if (trailingCodePattern.test(content)) {
            content = content.replace(trailingCodePattern, '$1');
            modified = true;
            console.log('✅ Fixed trailing code after handleDataTableRequest');
        }

        // Fix 6: Sửa lỗi async function syntax
        const asyncFunctionPattern = /(\w+):\s*async\s*\(/g;
        if (asyncFunctionPattern.test(content)) {
            content = content.replace(asyncFunctionPattern, '$1: async (');
            modified = true;
            console.log('✅ Fixed async function syntax');
        }

        if (modified) {
            fs.writeFileSync(filePath, content);
            console.log(`✅ Successfully fixed ${filePath}`);
        } else {
            console.log(`⚠️  No fixes needed for ${filePath}`);
        }

    } catch (error) {
        console.error(`❌ Error fixing ${filePath}:`, error.message);
    }
}

function fixSpecificPatterns() {
    console.log('\n🔧 Applying specific fixes...');

    // Fix adminController specific issues
    const adminPath = 'controllers/adminController.js';
    if (fs.existsSync(adminPath)) {
        let content = fs.readFileSync(adminPath, 'utf8');
        
        // Fix menuExampleList method
        const menuExamplePattern = /menuExampleList:\s*\(req, res\)\s*=>\s*\{[\s\S]*?const dataTableService[\s\S]*?\}\s*catch/;
        if (menuExamplePattern.test(content)) {
            const replacement = `menuExampleList: (req, res) => {
        try {
            const dataTableService = require('../services/dataTableService');
            
            // Cấu hình DataTable
            const config = {
                table: 'menu_example',
                columns: ['id', 'name_menu', 'created_by', 'created_at', 'share'],
                primaryKey: 'id',
                active: 0,
                activeOperator: '!=',
                filters: securityService.applyRoleBasedFiltering(req.user, {}),
                searchColumns: ['name_menu'],
                columnsMapping: [
                    '', // column 0 - actions
                    'name_menu', // column 1
                    'created_at', // column 2
                    'created_by_name', // column 3
                    'share' // column 4
                ],
                defaultOrder: [
                    { column: 'id', dir: 'DESC' }
                ],
                checkRole: false
            };

            // Function xử lý dữ liệu trước khi trả về
            const preprocessData = async (data) => {
                for (let item of data) {
                    if (item.created_by) {
                        const userRes = await commonService.getAllDataTable('user', { id: item.created_by });
                        if (userRes.success && userRes.data && userRes.data.length > 0) {
                            item.created_by_name = userRes.data[0].fullname;
                        } else {
                            item.created_by_name = 'N/A';
                        }
                    } else {
                        item.created_by_name = 'N/A';
                    }
                }
                return data;
            };

            // Xử lý request với preprocessData
            dataTableService.handleDataTableRequest(req, res, config, preprocessData);
        } catch`;
            
            content = content.replace(menuExamplePattern, replacement);
            fs.writeFileSync(adminPath, content);
            console.log('✅ Fixed menuExampleList method');
        }
    }
}

// Main execution
console.log('🚀 Starting controller error fixes...');

controllersToFix.forEach(filePath => {
    if (fs.existsSync(filePath)) {
        fixControllerErrors(filePath);
    } else {
        console.log(`⚠️  File not found: ${filePath}`);
    }
});

fixSpecificPatterns();

console.log('\n🎉 Controller error fixes completed!');
console.log('\n📝 Next steps:');
console.log('1. Check for any remaining syntax errors');
console.log('2. Test each DataTable functionality');
console.log('3. Verify all controllers work correctly');
