const moment = require('moment');
const commonService = require('../services/commonService');
const securityService = require('../services/securityService');
const dataTableService = require('../services/dataTableService');
const googleSheetsService = require('../services/googleSheetsService');

const surveyConfigController = {
    /**
     * Hi<PERSON>n thị danh sách cấu hình khảo sát của dự án
     */
    getList: async (req, res) => {
        try {
            const user = req.user;
            const projectId = req.params.projectId;
            const errors = [];
            
            // Kiểm tra dự án tồn tại và quyền truy cập
            const projectResponse = await commonService.getAllDataTable('projects', {
                id: projectId,
                active: [0, 1]
            });
            
            if (!projectResponse.success || !projectResponse.data || projectResponse.data.length === 0) {
                return res.render("error", {
                    user: user,
                    message: "<PERSON>hông tìm thấy dự án",
                    status: 404
                });
            }
            
            const project = projectResponse.data[0];
            if (!securityService.canAccessRecord(user, project)) {
                return res.render("error", {
                    user: user,
                    message: "Bạn không có quyền truy cập dự án này",
                    status: 403
                });
            }
            
            res.render('survey-configs/index', {
                user: user,
                errors: errors,
                project: project,
                moment: moment
            });
        } catch (error) {
            console.error('Error in surveyConfigController.getList:', error.message);
            return res.render("error", {
                user: req.user,
                message: "Có lỗi xảy ra khi tải trang",
                status: 500
            });
        }
    },

    /**
     * API lấy danh sách cấu hình khảo sát cho DataTable
     */
    getListTable: async (req, res) => {
        try {
            const user = req.user;
            const projectId = req.params.projectId;
            
            // Parse order từ request
            const order = commonService.parseDataTableOrder(req.body);
            
            // Order mặc định
            const defaultOrder = [
                { column: 'id', dir: 'DESC' }
            ];
            
            // Cấu hình DataTable
            const config = {
                table: 'survey_configs',
                columns: ['id', 'name', 'description', 'survey_url_slug', 'is_active', 'created_at'],
                primaryKey: 'id',
                active: -1,
                activeOperator: '!=',
                filters: {
                    project_id: projectId,
                    ...securityService.applyRoleBasedFiltering(user, {})
                },
                searchColumns: ['name', 'description', 'survey_url_slug'],
                columnsMapping: [
                    '', // checkbox column
                    'name',
                    'description',
                    'survey_url_slug',
                    'is_active',
                    'created_at',
                    '' // action column
                ],
                defaultOrder: defaultOrder,
                checkRole: false
            };
            
            // Gọi service xử lý DataTable
            await dataTableService.handleDataTableRequest(req, res, config, (data) => {
                // Format dữ liệu cho hiển thị
                return data.map(config => {
                    const statusText = config.is_active === 1 ? 'Hoạt động' : 'Tạm dừng';
                    const statusClass = config.is_active === 1 ? 'success' : 'warning';
                    
                    const surveyUrl = `${req.protocol}://${req.get('host')}/survey/${config.survey_url_slug}`;
                    
                    return [
                        `<input type="checkbox" class="select-row" value="${config.id}">`,
                        config.name,
                        config.description || '',
                        `<a href="${surveyUrl}" target="_blank" class="text-primary">${config.survey_url_slug}</a>`,
                        `<span class="badge badge-${statusClass}">${statusText}</span>`,
                        moment(config.created_at).format('DD/MM/YYYY HH:mm'),
                        `<div class="btn-group">
                            <button class="btn btn-sm btn-primary" onclick="editSurveyConfig(${config.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-info" onclick="configFields(${config.id})">
                                <i class="fas fa-cogs"></i>
                            </button>
                            <button class="btn btn-sm btn-success" onclick="viewResponses(${config.id})">
                                <i class="fas fa-chart-bar"></i>
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="copyLink('${surveyUrl}')">
                                <i class="fas fa-link"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteSurveyConfig(${config.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>`
                    ];
                });
            });
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json(securityService.createErrorResponse("Có lỗi xảy ra khi tải dữ liệu!"));
        }
    },

    /**
     * Hiển thị form tạo cấu hình khảo sát mới
     */
    getCreate: async (req, res) => {
        try {
            const user = req.user;
            const projectId = req.params.projectId;
            const errors = [];
            
            // Kiểm tra dự án
            const projectResponse = await commonService.getAllDataTable('projects', {
                id: projectId,
                active: [0, 1]
            });
            
            if (!projectResponse.success || !projectResponse.data || projectResponse.data.length === 0) {
                return res.render("error", {
                    user: user,
                    message: "Không tìm thấy dự án",
                    status: 404
                });
            }
            
            const project = projectResponse.data[0];
            if (!securityService.canAccessRecord(user, project)) {
                return res.render("error", {
                    user: user,
                    message: "Bạn không có quyền truy cập dự án này",
                    status: 403
                });
            }
            
            res.render('survey-configs/create', {
                user: user,
                errors: errors,
                project: project,
                surveyConfig: {},
                moment: moment
            });
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            return res.render("error");
        }
    },

    /**
     * Tạo cấu hình khảo sát mới
     */
    create: async (req, res) => {
        const resultData = {
            success: false,
            message: '',
            data: null
        };
        
        try {
            const user = req.user;
            const projectId = req.body.project_id;
            
            // Validation rules
            const validateRules = [
                { field: "name", type: "string", required: true, message: "Vui lòng nhập tên khảo sát!" },
                { field: "survey_url_slug", type: "string", required: true, message: "Vui lòng nhập slug URL!" },
                { field: "description", type: "string", required: false }
            ];
            
            const parameter = {
                project_id: projectId,
                name: req.body.name,
                description: req.body.description || null,
                survey_url_slug: req.body.survey_url_slug,
                is_active: parseInt(req.body.is_active) || 1,
                allow_multiple_responses: parseInt(req.body.allow_multiple_responses) || 0,
                require_email: parseInt(req.body.require_email) || 0,
                success_message: req.body.success_message || 'Cảm ơn bạn đã tham gia khảo sát!',
                settings: req.body.settings ? JSON.stringify(req.body.settings) : null,
                created_by: user.id,
                campaign_id: user.campaign_id
            };
            
            // Validate input
            const errors = securityService.validateInput(parameter, validateRules, { returnType: 'array' });
            if (errors.length > 0) {
                resultData.message = errors.map(s => s.message).join(', ');
                return res.json(resultData);
            }
            
            // Kiểm tra dự án tồn tại và quyền truy cập
            const projectResponse = await commonService.getAllDataTable('projects', {
                id: projectId,
                active: [0, 1]
            });
            
            if (!projectResponse.success || !projectResponse.data || projectResponse.data.length === 0) {
                resultData.message = 'Không tìm thấy dự án!';
                return res.json(resultData);
            }
            
            const project = projectResponse.data[0];
            if (!securityService.canAccessRecord(user, project)) {
                resultData.message = 'Bạn không có quyền tạo khảo sát cho dự án này!';
                return res.json(resultData);
            }
            
            // Kiểm tra slug URL đã tồn tại
            const existingSlug = await commonService.getAllDataTable('survey_configs', {
                survey_url_slug: parameter.survey_url_slug,
                active: [0, 1]
            });
            
            if (existingSlug.success && existingSlug.data && existingSlug.data.length > 0) {
                resultData.message = 'Slug URL đã tồn tại!';
                return res.json(resultData);
            }
            
            // Tạo cấu hình khảo sát
            const response = await commonService.addRecordTable(parameter, 'survey_configs', true);
            
            if (response.success && response.data) {
                resultData.success = true;
                resultData.message = 'Tạo cấu hình khảo sát thành công!';
                resultData.data = { id: response.data.insertId };
            } else {
                resultData.message = response.message || 'Có lỗi xảy ra khi tạo cấu hình khảo sát!';
            }
            
            res.json(resultData);
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json(securityService.createErrorResponse("Có lỗi xảy ra, vui lòng thử lại sau!"));
        }
    },

    /**
     * Hiển thị trang cấu hình các trường khảo sát
     */
    getFieldsConfig: async (req, res) => {
        try {
            const user = req.user;
            const surveyConfigId = req.params.id;
            const errors = [];

            // Lấy thông tin cấu hình khảo sát
            const configResponse = await commonService.getAllDataTable('survey_configs', {
                id: surveyConfigId,
                active: [0, 1]
            });

            if (!configResponse.success || !configResponse.data || configResponse.data.length === 0) {
                return res.render("error", {
                    user: user,
                    message: "Không tìm thấy cấu hình khảo sát",
                    status: 404
                });
            }

            const surveyConfig = configResponse.data[0];

            // Lấy thông tin dự án
            const projectResponse = await commonService.getAllDataTable('projects', {
                id: surveyConfig.project_id,
                active: [0, 1]
            });

            if (!projectResponse.success || !projectResponse.data || projectResponse.data.length === 0) {
                return res.render("error", {
                    user: user,
                    message: "Không tìm thấy dự án",
                    status: 404
                });
            }

            const project = projectResponse.data[0];
            if (!securityService.canAccessRecord(user, project)) {
                return res.render("error", {
                    user: user,
                    message: "Bạn không có quyền truy cập",
                    status: 403
                });
            }

            // Lấy danh sách các trường khảo sát
            const fieldsResponse = await commonService.getAllDataTable('survey_fields', {
                survey_config_id: surveyConfigId,
                active: 1
            }, 'display_order ASC');

            const surveyFields = fieldsResponse.success ? fieldsResponse.data : [];

            res.render('survey-configs/fields-config', {
                user: user,
                errors: errors,
                project: project,
                surveyConfig: surveyConfig,
                surveyFields: surveyFields,
                moment: moment
            });
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            return res.render("error");
        }
    },

    /**
     * Lưu cấu hình các trường khảo sát
     */
    saveFieldsConfig: async (req, res) => {
        const resultData = {
            success: false,
            message: '',
            data: null
        };

        try {
            const user = req.user;
            const surveyConfigId = req.body.survey_config_id;
            const fields = req.body.fields || [];

            // Kiểm tra cấu hình khảo sát tồn tại và quyền truy cập
            const configResponse = await commonService.getAllDataTable('survey_configs', {
                id: surveyConfigId,
                active: [0, 1]
            });

            if (!configResponse.success || !configResponse.data || configResponse.data.length === 0) {
                resultData.message = 'Không tìm thấy cấu hình khảo sát!';
                return res.json(resultData);
            }

            const surveyConfig = configResponse.data[0];

            // Kiểm tra quyền truy cập dự án
            const projectResponse = await commonService.getAllDataTable('projects', {
                id: surveyConfig.project_id,
                active: [0, 1]
            });

            if (!projectResponse.success || !projectResponse.data || projectResponse.data.length === 0) {
                resultData.message = 'Không tìm thấy dự án!';
                return res.json(resultData);
            }

            const project = projectResponse.data[0];
            if (!securityService.canAccessRecord(user, project)) {
                resultData.message = 'Bạn không có quyền chỉnh sửa!';
                return res.json(resultData);
            }

            // Xóa tất cả các trường cũ (soft delete)
            await commonService.updateRecordTable(
                { active: -1, updated_at: new Date() },
                'survey_fields',
                { survey_config_id: surveyConfigId }
            );

            // Thêm các trường mới
            for (let i = 0; i < fields.length; i++) {
                const field = fields[i];

                const fieldData = {
                    survey_config_id: surveyConfigId,
                    field_name: field.field_name,
                    field_label: field.field_label,
                    field_type: field.field_type,
                    field_options: field.field_options ? JSON.stringify(field.field_options) : null,
                    is_required: field.is_required ? 1 : 0,
                    placeholder: field.placeholder || null,
                    help_text: field.help_text || null,
                    validation_rules: field.validation_rules ? JSON.stringify(field.validation_rules) : null,
                    display_order: i + 1,
                    is_active: 1,
                    field_settings: field.field_settings ? JSON.stringify(field.field_settings) : null,
                    created_by: user.id
                };

                await commonService.addRecordTable(fieldData, 'survey_fields', false);
            }

            // Cập nhật headers trong Google Sheet nếu có
            if (project.google_sheet_id) {
                try {
                    await googleSheetsService.updateSheetHeaders(project.google_sheet_id, fields);
                } catch (sheetError) {
                    console.warn('Warning: Could not update Google Sheet headers:', sheetError.message);
                }
            }

            resultData.success = true;
            resultData.message = 'Lưu cấu hình trường thành công!';

            res.json(resultData);
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json(securityService.createErrorResponse("Có lỗi xảy ra, vui lòng thử lại sau!"));
        }
    },

    /**
     * Lấy danh sách template khảo sát
     */
    getTemplates: async (req, res) => {
        try {
            const user = req.user;

            // Lấy templates public và của user
            const templatesResponse = await commonService.getAllDataTable('survey_templates', {
                active: 1,
                $or: [
                    { is_public: 1 },
                    { created_by: user.id }
                ]
            }, 'usage_count DESC, created_at DESC');

            const templates = templatesResponse.success ? templatesResponse.data : [];

            res.json({
                success: true,
                data: templates
            });
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json(securityService.createErrorResponse("Có lỗi xảy ra khi tải templates!"));
        }
    }
};

module.exports = surveyConfigController;
