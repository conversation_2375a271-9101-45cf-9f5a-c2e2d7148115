/**
 * Debug Routes Test
 * <PERSON><PERSON><PERSON> tra các routes và permissions
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

async function testProjectRoutes() {
    console.log('🔍 Testing Project Routes and Permissions...\n');
    
    try {
        const securityService = require('../services/securityService');
        const commonService = require('../services/commonService');
        
        // Test user mock - cần có role_id array
        const testUser = {
            id: 1,
            campaign_id: 1,
            role_id: [3], // Array of role IDs
            isAdmin: false
        };

        console.log('1. Testing user permissions...');

        // Test permissions using checkAuthorization
        const projectReadAuth = securityService.checkAuthorization(testUser, 'projects', 'read');
        const projectWriteAuth = securityService.checkAuthorization(testUser, 'projects', 'write');
        const surveyReadAuth = securityService.checkAuthorization(testUser, 'survey-configs', 'read');
        const surveyWriteAuth = securityService.checkAuthorization(testUser, 'survey-configs', 'write');

        console.log(`   Projects read: ${projectReadAuth.authorized}`);
        console.log(`   Projects write: ${projectWriteAuth.authorized}`);
        console.log(`   Survey-configs read: ${surveyReadAuth.authorized}`);
        console.log(`   Survey-configs write: ${surveyWriteAuth.authorized}`);

        if (!projectReadAuth.authorized || !surveyReadAuth.authorized) {
            console.log('❌ User does not have required permissions!');
            console.log('   This is likely the cause of 404 errors.');
            console.log('   Check securityService.js role permissions.');
            console.log('   Errors:', projectReadAuth.errors, surveyReadAuth.errors);
        } else {
            console.log('✅ User has required permissions');
        }
        
        console.log('\n2. Testing record access...');
        
        // Test record access
        const mockProject = {
            id: 2,
            name: 'Test Project',
            created_by: 1, // Same as test user
            campaign_id: 1
        };
        
        const canAccess = securityService.canAccessRecord(testUser, mockProject);
        console.log(`   Can access project: ${canAccess}`);
        
        if (!canAccess) {
            console.log('❌ User cannot access record!');
            console.log('   Check created_by field matches user.id');
        } else {
            console.log('✅ User can access record');
        }
        
        console.log('\n3. Testing role-based filtering...');
        
        const filters = securityService.applyRoleBasedFiltering(testUser, {});
        console.log('   Applied filters:', JSON.stringify(filters, null, 2));
        
        console.log('\n4. Testing database query...');
        
        // Test actual database query
        try {
            const projectResponse = await commonService.getAllDataTable('projects', {
                id: 2,
                active: [0, 1]
            });
            
            console.log(`   Database query success: ${projectResponse.success}`);
            console.log(`   Records found: ${projectResponse.data ? projectResponse.data.length : 0}`);
            
            if (projectResponse.success && projectResponse.data && projectResponse.data.length > 0) {
                const project = projectResponse.data[0];
                console.log(`   Project found: ${project.name}`);
                console.log(`   Created by: ${project.created_by}`);
                console.log(`   Campaign ID: ${project.campaign_id}`);
                
                const canAccessReal = securityService.canAccessRecord(testUser, project);
                console.log(`   Can access real project: ${canAccessReal}`);
            } else {
                console.log('❌ No project found with ID 2');
                console.log('   This could be the cause of 404 error');
            }
        } catch (dbError) {
            console.log('❌ Database error:', dbError.message);
        }
        
    } catch (error) {
        console.log('❌ Test failed:', error.message);
        console.log('Stack:', error.stack);
    }
}

async function testRouteMiddleware() {
    console.log('\n🔍 Testing Route Middleware...\n');
    
    try {
        const securityService = require('../services/securityService');
        
        // Mock request/response
        const mockReq = {
            user: {
                id: 1,
                campaign_id: 1,
                role_id: [3], // Array of role IDs
                isAdmin: false
            },
            params: { id: 2, projectId: 2 }
        };
        
        const mockRes = {
            status: (code) => {
                console.log(`   Response status: ${code}`);
                return {
                    json: (data) => {
                        console.log(`   Response data:`, data);
                    }
                };
            }
        };
        
        const mockNext = () => {
            console.log('   ✅ Middleware passed, calling next()');
        };
        
        console.log('1. Testing requirePermission middleware...');
        
        // Test projects read permission
        console.log('   Testing projects read permission...');
        const projectsReadMiddleware = securityService.requirePermission('projects', 'read');
        projectsReadMiddleware(mockReq, mockRes, mockNext);
        
        // Test survey-configs read permission
        console.log('   Testing survey-configs read permission...');
        const surveyReadMiddleware = securityService.requirePermission('survey-configs', 'read');
        surveyReadMiddleware(mockReq, mockRes, mockNext);
        
    } catch (error) {
        console.log('❌ Middleware test failed:', error.message);
    }
}

async function runDebugTests() {
    console.log('🚀 Starting Route Debug Tests\n');
    console.log('=' .repeat(50));
    
    await testProjectRoutes();
    
    console.log('\n' + '=' .repeat(50));
    
    await testRouteMiddleware();
    
    console.log('\n' + '=' .repeat(50));
    console.log('📋 Debug Summary:');
    console.log('1. Check if user has required permissions');
    console.log('2. Check if project exists in database');
    console.log('3. Check if user can access the project record');
    console.log('4. Check middleware is working correctly');
    
    console.log('\n🔧 Common fixes:');
    console.log('1. Add permissions to user role in securityService.js');
    console.log('2. Ensure project.created_by matches user.id');
    console.log('3. Check database connection and table exists');
    console.log('4. Verify routes are properly defined');
    
    console.log('\n✅ Debug tests completed!');
}

// Chạy debug tests
if (require.main === module) {
    runDebugTests().catch(console.error);
}

module.exports = {
    testProjectRoutes,
    testRouteMiddleware
};
