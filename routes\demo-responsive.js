/**
 * Demo route for responsive table testing
 * Route: /demo/responsive-table
 */

const express = require('express');
const router = express.Router();

// Demo data
const demoPatients = [
    {
        id: 1,
        fullname: '<PERSON><PERSON><PERSON><PERSON>n <PERSON>',
        phone: '**********',
        room: '101',
        diagnosis: 'Viêm gan B',
        date: '2024-03-15',
        status: 'active'
    },
    {
        id: 2,
        fullname: 'Trần Thị B',
        phone: '**********',
        room: '102',
        diagnosis: 'Ung thư gan',
        date: '2024-03-16',
        status: 'urgent'
    },
    {
        id: 3,
        fullname: '<PERSON><PERSON>n <PERSON>',
        phone: '**********',
        room: '103',
        diagnosis: 'Xơ gan',
        date: '2024-03-17',
        status: 'active'
    },
    {
        id: 4,
        fullname: '<PERSON>ạ<PERSON> Thị D',
        phone: '**********',
        room: '104',
        diagnosis: 'Viêm gan C',
        date: '2024-03-18',
        status: 'inactive'
    },
    {
        id: 5,
        fullname: '<PERSON><PERSON><PERSON>',
        phone: '**********',
        room: '105',
        diagnosis: '<PERSON><PERSON> <PERSON><PERSON> mỡ',
        date: '2024-03-19',
        status: 'active'
    }
];

// GET route - Display demo page
router.get('/', (req, res) => {
    try {
        res.render('demo/responsive-table', {
            title: 'Demo Responsive Table',
            patients: demoPatients,
            user: req.user || { id: 1, isAdmin: false }
        });
    } catch (error) {
        console.error('Error rendering demo page:', error);
        res.status(500).send('Error loading demo page');
    }
});

// POST route - DataTables AJAX endpoint
router.post('/data', (req, res) => {
    try {
        const draw = parseInt(req.body.draw) || 1;
        const start = parseInt(req.body.start) || 0;
        const length = parseInt(req.body.length) || 10;
        const searchValue = req.body.search?.value || '';

        // Filter data based on search
        let filteredData = demoPatients;
        if (searchValue) {
            filteredData = demoPatients.filter(patient => 
                patient.fullname.toLowerCase().includes(searchValue.toLowerCase()) ||
                patient.phone.includes(searchValue) ||
                patient.diagnosis.toLowerCase().includes(searchValue.toLowerCase()) ||
                patient.room.includes(searchValue)
            );
        }

        // Pagination
        const paginatedData = filteredData.slice(start, start + length);

        // Format data for DataTables
        const formattedData = paginatedData.map(patient => ({
            id: patient.id,
            fullname: patient.fullname,
            phone: patient.phone,
            room: patient.room,
            diagnosis: patient.diagnosis,
            date: patient.date,
            status: patient.status
        }));

        res.json({
            draw: draw,
            recordsTotal: demoPatients.length,
            recordsFiltered: filteredData.length,
            data: formattedData
        });
    } catch (error) {
        console.error('Error processing DataTables request:', error);
        res.status(500).json({
            error: 'Internal server error',
            draw: req.body.draw || 1,
            recordsTotal: 0,
            recordsFiltered: 0,
            data: []
        });
    }
});

module.exports = router;
