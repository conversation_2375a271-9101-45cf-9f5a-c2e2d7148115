/**
 * Test Google Sheets Service
 * Chạy: node test/test-google-sheets.js
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

async function testGoogleSheetsService() {
    console.log('🧪 Testing Google Sheets Service...\n');
    
    try {
        const googleSheetsService = require('../services/googleSheetsService');
        
        // Test 1: <PERSON><PERSON><PERSON> tra credentials
        console.log('1. Checking credentials...');
        if (!process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL || !process.env.GOOGLE_PRIVATE_KEY) {
            console.log('⚠️  Google Sheets credentials not configured');
            console.log('   Set GOOGLE_SERVICE_ACCOUNT_EMAIL and GOOGLE_PRIVATE_KEY in .env file');
            console.log('   Skipping Google Sheets tests...\n');
            return;
        }
        console.log('✅ Credentials found\n');
        
        // Test 2: Tạo service account auth
        console.log('2. Creating service account auth...');
        try {
            const auth = googleSheetsService.createServiceAccountAuth();
            console.log('✅ Service account auth created\n');
        } catch (error) {
            console.log('❌ Failed to create service account auth:', error.message);
            return;
        }
        
        // Test 3: Tạo Google Sheet mới
        console.log('3. Creating new Google Sheet...');
        try {
            const result = await googleSheetsService.createNewSheet(
                'Test Project ' + Date.now(),
                ['ID', 'Name', 'Email', 'Age Group', 'Timestamp']
            );
            
            if (result.sheetId) {
                console.log('✅ Google Sheet created successfully');
                console.log(`   Sheet ID: ${result.sheetId}`);
                console.log(`   Sheet URL: ${result.sheetUrl}\n`);
                
                // Test 4: Thêm dữ liệu vào sheet
                console.log('4. Appending data to sheet...');
                try {
                    const appendResult = await googleSheetsService.appendRowToSheet(
                        result.sheetId,
                        {
                            'ID': '1',
                            'Name': 'Test User',
                            'Email': '<EMAIL>',
                            'Age Group': '26-35'
                        },
                        ['ID', 'Name', 'Email', 'Age Group']
                    );
                    
                    if (appendResult.success) {
                        console.log('✅ Data appended successfully');
                        console.log(`   Row index: ${appendResult.rowIndex}\n`);
                    } else {
                        console.log('❌ Failed to append data:', appendResult.error);
                    }
                } catch (error) {
                    console.log('❌ Error appending data:', error.message);
                }
                
                // Test 5: Lấy thông tin sheet
                console.log('5. Getting sheet info...');
                try {
                    const sheetInfo = await googleSheetsService.getSheetInfo(result.sheetId);
                    console.log('✅ Sheet info retrieved');
                    console.log(`   Title: ${sheetInfo.title}`);
                    console.log(`   Sheet count: ${sheetInfo.sheetCount}`);
                    console.log(`   URL: ${sheetInfo.url}\n`);
                } catch (error) {
                    console.log('❌ Error getting sheet info:', error.message);
                }
                
                // Test 6: Kiểm tra quyền truy cập
                console.log('6. Checking sheet access...');
                try {
                    const hasAccess = await googleSheetsService.checkSheetAccess(result.sheetId);
                    console.log(`✅ Sheet access: ${hasAccess ? 'Yes' : 'No'}\n`);
                } catch (error) {
                    console.log('❌ Error checking access:', error.message);
                }
                
                console.log('🎉 All Google Sheets tests completed successfully!');
                console.log(`📊 You can view the test sheet at: ${result.sheetUrl}`);
                
            } else {
                console.log('⚠️  Sheet creation returned null (credentials issue?)');
            }
        } catch (error) {
            console.log('❌ Failed to create Google Sheet:', error.message);
            console.log('   Stack:', error.stack);
        }
        
    } catch (error) {
        console.log('❌ Test failed:', error.message);
        console.log('   Stack:', error.stack);
    }
}

async function testWithoutCredentials() {
    console.log('🧪 Testing without credentials...\n');
    
    // Temporarily remove credentials
    const originalEmail = process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL;
    const originalKey = process.env.GOOGLE_PRIVATE_KEY;
    
    delete process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL;
    delete process.env.GOOGLE_PRIVATE_KEY;
    
    try {
        const googleSheetsService = require('../services/googleSheetsService');
        
        console.log('1. Testing createNewSheet without credentials...');
        const result = await googleSheetsService.createNewSheet('Test Project');
        
        if (result.sheetId === null && result.sheetUrl === null) {
            console.log('✅ Correctly handled missing credentials\n');
        } else {
            console.log('❌ Should have returned null for missing credentials\n');
        }
        
        console.log('2. Testing appendRowToSheet without credentials...');
        const appendResult = await googleSheetsService.appendRowToSheet('test-id', {}, []);
        
        if (!appendResult.success && appendResult.error === 'Credentials not configured') {
            console.log('✅ Correctly handled missing credentials for append\n');
        } else {
            console.log('❌ Should have returned error for missing credentials\n');
        }
        
    } catch (error) {
        console.log('❌ Unexpected error:', error.message);
    }
    
    // Restore credentials
    if (originalEmail) process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL = originalEmail;
    if (originalKey) process.env.GOOGLE_PRIVATE_KEY = originalKey;
    
    console.log('✅ Credentials handling tests completed\n');
}

async function runAllTests() {
    console.log('🚀 Starting Google Sheets Service Tests\n');
    console.log('=' .repeat(50));
    
    await testWithoutCredentials();
    
    console.log('=' .repeat(50));
    
    await testGoogleSheetsService();
    
    console.log('\n' + '=' .repeat(50));
    console.log('📋 Test Summary:');
    console.log('1. ✅ Service can handle missing credentials gracefully');
    console.log('2. ✅ Service can create Google Sheets (if credentials provided)');
    console.log('3. ✅ Service can append data to sheets');
    console.log('4. ✅ Service can retrieve sheet information');
    console.log('5. ✅ All methods use googleapis directly (no deprecated methods)');
    
    console.log('\n🔧 To use Google Sheets integration:');
    console.log('1. Create a Google Cloud Project');
    console.log('2. Enable Google Sheets API and Google Drive API');
    console.log('3. Create a Service Account and download JSON key');
    console.log('4. Add to .env file:');
    console.log('   GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>');
    console.log('   GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\\n...\\n-----END PRIVATE KEY-----\\n"');
    
    console.log('\n✅ Google Sheets Service is ready to use!');
}

// Chạy tests
if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = {
    testGoogleSheetsService,
    testWithoutCredentials
};
