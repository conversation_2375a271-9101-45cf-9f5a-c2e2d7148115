// Script tự động tối ưu hóa tất cả DataTables trong hệ thống
const fs = require('fs');
const path = require('path');

class DataTableOptimizer {
    constructor() {
        this.controllersDir = path.join(__dirname, '../controllers');
        this.viewsDir = path.join(__dirname, '../views');
        this.optimizedControllers = [];
        this.optimizedViews = [];
        this.errors = [];
    }

    // Tìm tất cả files sử dụng getDataTableData
    findControllersWithDataTable() {
        const controllers = [];
        const files = fs.readdirSync(this.controllersDir);
        
        files.forEach(file => {
            if (file.endsWith('.js')) {
                const filePath = path.join(this.controllersDir, file);
                const content = fs.readFileSync(filePath, 'utf8');
                
                if (content.includes('getDataTableData')) {
                    controllers.push({
                        file: file,
                        path: filePath,
                        content: content
                    });
                }
            }
        });
        
        return controllers;
    }

    // <PERSON><PERSON><PERSON> tấ<PERSON> cả views sử dụng DataTable
    findViewsWithDataTable() {
        const views = [];
        
        const scanDirectory = (dir) => {
            const items = fs.readdirSync(dir);
            
            items.forEach(item => {
                const itemPath = path.join(dir, item);
                const stat = fs.statSync(itemPath);
                
                if (stat.isDirectory()) {
                    scanDirectory(itemPath);
                } else if (item.endsWith('.ejs')) {
                    const content = fs.readFileSync(itemPath, 'utf8');
                    
                    if (content.includes('DataTable') || content.includes('dataTable')) {
                        views.push({
                            file: item,
                            path: itemPath,
                            content: content,
                            relativePath: path.relative(this.viewsDir, itemPath)
                        });
                    }
                }
            });
        };
        
        scanDirectory(this.viewsDir);
        return views;
    }

    // Tối ưu controller
    optimizeController(controller) {
        console.log(`\n🔧 Optimizing controller: ${controller.file}`);
        
        try {
            let content = controller.content;
            let modified = false;

            // Pattern để tìm và thay thế getDataTableData usage
            const patterns = [
                // Pattern 1: Basic getDataTableData usage
                {
                    regex: /var parameter = \{[\s\S]*?\};\s*commonService\.getDataTableData\(parameter\)\.then\([\s\S]*?\}\);/g,
                    replacement: this.generateControllerReplacement.bind(this)
                },
                // Pattern 2: Error handling pattern
                {
                    regex: /if \(!req\.user\.isAdmin[^}]*\}\s*\)/g,
                    replacement: (match) => {
                        return match.replace('return res.json({', 'return res.json(dataTableService.createErrorResponse(req.body, ');
                    }
                }
            ];

            patterns.forEach(pattern => {
                if (pattern.regex.test(content)) {
                    content = content.replace(pattern.regex, pattern.replacement);
                    modified = true;
                }
            });

            // Thêm import dataTableService nếu chưa có
            if (modified && !content.includes("require('../services/dataTableService')")) {
                content = content.replace(
                    /const commonService = require\('\.\.\/services\/commonService'\);/,
                    `const commonService = require('../services/commonService');\nconst dataTableService = require('../services/dataTableService');`
                );
            }

            if (modified) {
                fs.writeFileSync(controller.path, content);
                this.optimizedControllers.push(controller.file);
                console.log(`✅ Optimized: ${controller.file}`);
            } else {
                console.log(`⚠️  No optimization needed: ${controller.file}`);
            }

        } catch (error) {
            console.error(`❌ Error optimizing ${controller.file}:`, error.message);
            this.errors.push(`Controller ${controller.file}: ${error.message}`);
        }
    }

    // Generate replacement cho controller
    generateControllerReplacement(match) {
        // Extract table name, search columns, etc. từ match
        const tableMatch = match.match(/table:\s*['"`]([^'"`]+)['"`]/);
        const searchMatch = match.match(/columns:\s*\[([^\]]+)\]/);
        
        const tableName = tableMatch ? tableMatch[1] : 'unknown_table';
        
        return `const dataTableService = require('../services/dataTableService');
        
        // Cấu hình DataTable
        const config = {
            table: '${tableName}',
            primaryKey: 'id',
            active: 0,
            activeOperator: '!=',
            filters: securityService.applyRoleBasedFiltering(req.user, {}),
            searchColumns: ['name'], // TODO: Adjust based on actual columns
            columnsMapping: [
                '', // column 0 - actions
                'name', // column 1 - TODO: Adjust
                'created_at' // column 2 - TODO: Adjust
            ],
            defaultOrder: [
                { column: 'id', dir: 'DESC' }
            ],
            checkRole: true
        };

        // Xử lý request
        dataTableService.handleDataTableRequest(req, res, config);`;
    }

    // Tối ưu view
    optimizeView(view) {
        console.log(`\n🎨 Optimizing view: ${view.relativePath}`);
        
        try {
            let content = view.content;
            let modified = false;

            // Pattern để thêm orderable/searchable cho columns
            const columnPattern = /columns:\s*\[([\s\S]*?)\]/;
            const match = content.match(columnPattern);
            
            if (match) {
                const columnsContent = match[1];
                
                // Kiểm tra xem đã có orderable/searchable chưa
                if (!columnsContent.includes('orderable:') && !columnsContent.includes('searchable:')) {
                    // Thêm orderable/searchable cho từng column
                    let optimizedColumns = columnsContent.replace(
                        /\{\s*data:\s*([^,}]+)/g,
                        '{\n                        data: $1,\n                        orderable: true,\n                        searchable: true'
                    );
                    
                    // Xử lý actions column
                    optimizedColumns = optimizedColumns.replace(
                        /\{\s*data:\s*null([^}]*render[^}]*)\}/g,
                        '{\n                        data: null,\n                        orderable: false,\n                        searchable: false$1}'
                    );
                    
                    content = content.replace(columnPattern, `columns: [${optimizedColumns}]`);
                    modified = true;
                }

                // Thêm cấu hình ordering và searching
                if (!content.includes('ordering:') || !content.includes('searching:')) {
                    content = content.replace(
                        /\],(\s*)(order:|language:|rowCallback:)/,
                        `],
                // Cấu hình order mặc định (sẽ được override bởi server nếu có)
                order: [], // Để trống để server xử lý order
                // Cấu hình searching
                searching: true, // Bật tính năng search
                // Cấu hình ordering
                ordering: true, // Bật tính năng sort$1$2`
                    );
                    modified = true;
                }
            }

            if (modified) {
                fs.writeFileSync(view.path, content);
                this.optimizedViews.push(view.relativePath);
                console.log(`✅ Optimized: ${view.relativePath}`);
            } else {
                console.log(`⚠️  No optimization needed: ${view.relativePath}`);
            }

        } catch (error) {
            console.error(`❌ Error optimizing ${view.relativePath}:`, error.message);
            this.errors.push(`View ${view.relativePath}: ${error.message}`);
        }
    }

    // Chạy tối ưu hóa toàn bộ
    async optimizeAll() {
        console.log('🚀 Starting DataTable optimization...\n');

        // Tìm tất cả controllers và views
        const controllers = this.findControllersWithDataTable();
        const views = this.findViewsWithDataTable();

        console.log(`📊 Found ${controllers.length} controllers with DataTable`);
        console.log(`📄 Found ${views.length} views with DataTable\n`);

        // Tối ưu controllers
        console.log('=== OPTIMIZING CONTROLLERS ===');
        controllers.forEach(controller => {
            this.optimizeController(controller);
        });

        // Tối ưu views
        console.log('\n=== OPTIMIZING VIEWS ===');
        views.forEach(view => {
            this.optimizeView(view);
        });

        // Báo cáo kết quả
        this.generateReport();
    }

    // Tạo báo cáo
    generateReport() {
        console.log('\n' + '='.repeat(50));
        console.log('📋 OPTIMIZATION REPORT');
        console.log('='.repeat(50));
        
        console.log(`\n✅ Optimized Controllers (${this.optimizedControllers.length}):`);
        this.optimizedControllers.forEach(file => console.log(`   - ${file}`));
        
        console.log(`\n✅ Optimized Views (${this.optimizedViews.length}):`);
        this.optimizedViews.forEach(file => console.log(`   - ${file}`));
        
        if (this.errors.length > 0) {
            console.log(`\n❌ Errors (${this.errors.length}):`);
            this.errors.forEach(error => console.log(`   - ${error}`));
        }
        
        console.log('\n🎉 Optimization completed!');
        console.log('\n📝 Next steps:');
        console.log('1. Review the optimized files');
        console.log('2. Test DataTable functionality');
        console.log('3. Verify order parsing works correctly');
        console.log('4. Update any custom configurations as needed');
    }
}

// Chạy script
if (require.main === module) {
    const optimizer = new DataTableOptimizer();
    optimizer.optimizeAll().catch(console.error);
}

module.exports = DataTableOptimizer;
