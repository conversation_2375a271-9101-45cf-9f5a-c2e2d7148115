/**
 * Test luồng tạo kh<PERSON>o sát đơn giản
 * Chạy: node test/survey-flow-test.js
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// Mock các service cần thiết
const mockUser = {
    id: 1,
    campaign_id: 1,
    role: 'admin'
};

const mockReq = {
    user: mockUser,
    body: {},
    ip: '127.0.0.1',
    get: (header) => {
        const headers = {
            'User-Agent': 'Test Browser',
            'Referrer': 'http://localhost:3000'
        };
        return headers[header] || '';
    },
    sessionID: 'test-session-123'
};

const mockRes = {
    json: (data) => {
        console.log('Response:', JSON.stringify(data, null, 2));
        return data;
    }
};

async function testProjectCreation() {
    console.log('\n=== Test 1: Tạo Dự án ===');
    
    try {
        const projectController = require('../controllers/projectController');
        
        mockReq.body = {
            name: 'Test Project ' + Date.now(),
            description: 'Test project description',
            status: 1,
            start_date: '2025-01-01',
            end_date: '2025-12-31'
        };
        
        await projectController.create(mockReq, mockRes);
        console.log('✓ Project creation test completed');
    } catch (error) {
        console.error('✗ Project creation failed:', error.message);
    }
}

async function testSurveyConfigCreation() {
    console.log('\n=== Test 2: Tạo Cấu hình Khảo sát ===');
    
    try {
        const surveyConfigController = require('../controllers/surveyConfigController');
        
        mockReq.body = {
            project_id: 1, // Giả sử project ID = 1
            name: 'Test Survey ' + Date.now(),
            description: 'Test survey description',
            survey_url_slug: 'test-survey-' + Date.now(),
            is_active: 1,
            allow_multiple_responses: 0,
            require_email: 1,
            success_message: 'Thank you for participating!'
        };
        
        await surveyConfigController.create(mockReq, mockRes);
        console.log('✓ Survey config creation test completed');
    } catch (error) {
        console.error('✗ Survey config creation failed:', error.message);
    }
}

async function testFieldsConfiguration() {
    console.log('\n=== Test 3: Cấu hình Trường Khảo sát ===');
    
    try {
        const surveyConfigController = require('../controllers/surveyConfigController');
        
        mockReq.body = {
            survey_config_id: 1, // Giả sử survey config ID = 1
            fields: [
                {
                    field_name: 'full_name',
                    field_label: 'Họ và tên',
                    field_type: 'text',
                    is_required: true,
                    placeholder: 'Nhập họ và tên của bạn'
                },
                {
                    field_name: 'email',
                    field_label: 'Email',
                    field_type: 'email',
                    is_required: true,
                    placeholder: '<EMAIL>'
                },
                {
                    field_name: 'age_group',
                    field_label: 'Nhóm tuổi',
                    field_type: 'select',
                    is_required: true,
                    field_options: [
                        { value: '18-25', label: '18-25 tuổi' },
                        { value: '26-35', label: '26-35 tuổi' },
                        { value: '36-45', label: '36-45 tuổi' },
                        { value: '46+', label: 'Trên 45 tuổi' }
                    ]
                }
            ]
        };
        
        await surveyConfigController.saveFieldsConfig(mockReq, mockRes);
        console.log('✓ Fields configuration test completed');
    } catch (error) {
        console.error('✗ Fields configuration failed:', error.message);
    }
}

async function testGoogleSheetsService() {
    console.log('\n=== Test 4: Google Sheets Service ===');
    
    try {
        const googleSheetsService = require('../services/googleSheetsService');
        
        // Test tạo sheet mới
        console.log('Testing createNewSheet...');
        const result = await googleSheetsService.createNewSheet('Test Project', ['Name', 'Email', 'Age Group']);
        
        if (result.sheetId) {
            console.log('✓ Google Sheet created successfully');
            console.log('  Sheet ID:', result.sheetId);
            console.log('  Sheet URL:', result.sheetUrl);
            
            // Test thêm dữ liệu
            console.log('Testing appendRowToSheet...');
            const appendResult = await googleSheetsService.appendRowToSheet(
                result.sheetId,
                {
                    'Name': 'Test User',
                    'Email': '<EMAIL>',
                    'Age Group': '26-35'
                },
                ['Name', 'Email', 'Age Group']
            );
            
            if (appendResult.success) {
                console.log('✓ Data appended to Google Sheet successfully');
            } else {
                console.log('⚠ Data append failed:', appendResult.error);
            }
        } else {
            console.log('⚠ Google Sheet creation skipped (credentials not configured)');
        }
    } catch (error) {
        console.error('✗ Google Sheets test failed:', error.message);
    }
}

async function testSurveySubmission() {
    console.log('\n=== Test 5: Submit Khảo sát ===');
    
    try {
        const surveyController = require('../controllers/surveyController');
        
        // Mock params
        mockReq.params = { slug: 'test-survey-123' };
        mockReq.body = {
            full_name: 'Test User',
            email: '<EMAIL>',
            age_group: '26-35'
        };
        
        await surveyController.submitPublicSurvey(mockReq, mockRes);
        console.log('✓ Survey submission test completed');
    } catch (error) {
        console.error('✗ Survey submission failed:', error.message);
    }
}

async function runAllTests() {
    console.log('🚀 Bắt đầu test luồng hệ thống khảo sát...');
    console.log('📝 Lưu ý: Một số test có thể fail do chưa có dữ liệu thực tế trong database');
    
    await testProjectCreation();
    await testSurveyConfigCreation();
    await testFieldsConfiguration();
    await testGoogleSheetsService();
    await testSurveySubmission();
    
    console.log('\n✅ Hoàn thành tất cả tests!');
    console.log('\n📋 Checklist để hệ thống hoạt động đầy đủ:');
    console.log('  1. ✓ Database migration đã chạy');
    console.log('  2. ✓ Controllers và services đã được tạo');
    console.log('  3. ✓ Routes đã được thêm vào index.js');
    console.log('  4. ✓ Views đã được tạo với layout đúng');
    console.log('  5. ⚠ Google Sheets credentials cần được cấu hình (tùy chọn)');
    console.log('  6. ✓ CSS và JS đã được tạo');
    
    console.log('\n🔧 Để test thực tế:');
    console.log('  1. Khởi động server: npm start');
    console.log('  2. Truy cập: http://localhost:3000/projects');
    console.log('  3. Tạo dự án mới');
    console.log('  4. Tạo khảo sát cho dự án');
    console.log('  5. Cấu hình các trường khảo sát');
    console.log('  6. Test form công khai tại URL được tạo');
}

// Chạy tests
if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = {
    testProjectCreation,
    testSurveyConfigCreation,
    testFieldsConfiguration,
    testGoogleSheetsService,
    testSurveySubmission
};
