// Template cho DataTable controller với order configuration
// Thay thế các placeholder bằng giá trị thực tế

const commonService = require('../services/commonService');
const securityService = require('../services/securityService');

module.exports = {
    list: function(req, res, next) {
        try {
            // Kiểm tra quyền truy cập (tùy chỉnh theo từng controller)
            let checkRole = commonService.checkRoleUser(req.body.path, req.user);
            if(checkRole.length > 0){
                return res.json({
                    draw: req.body.draw || 1,
                    recordsTotal: 0,
                    recordsFiltered: 0,
                    data: [],
                    error: checkRole.join(', ')
                });
            }

            // Định nghĩa columns mapping cho DataTables
            // Thay thế bằng các column thực tế của bảng
            const columnsMapping = [
                '', // column 0 - actions column (không sort được)
                'COLUMN_NAME_1', // column 1 - thay bằng tên column thực tế
                'COLUMN_NAME_2', // column 2 - thay bằng tên column thực tế
                'COLUMN_NAME_3', // column 3 - thay bằng tên column thực tế
                // Thêm các column khác...
            ];
            
            // Order mặc định - tùy chỉnh theo logic business
            const defaultOrder = [
                {
                    column: 'PRIORITY_COLUMN', // Thay bằng column ưu tiên cao nhất
                    dir: 'DESC'
                },
                {
                    column: 'SECONDARY_COLUMN', // Thay bằng column ưu tiên thứ hai
                    dir: 'DESC'
                },
                {
                    column: 'id', // id cuối cùng
                    dir: 'DESC'
                }
            ];
            
            // Parse order từ req.body sử dụng helper function
            const finalOrder = commonService.parseDataTableOrder(req.body, columnsMapping, defaultOrder);
            
            var parameter = {
                table: 'TABLE_NAME', // Thay bằng tên bảng thực tế
                columns: ['COLUMN_LIST'], // Thay bằng danh sách columns cần select
                primaryKey: 'id',
                active: 0, // Tùy chỉnh theo logic active/inactive
                activeOperator: '!=',
                filters: securityService.applyRoleBasedFiltering(req.user, {
                    // Thêm các filter cần thiết
                }),
                search: {
                    value: req.body['search[value]'],
                    columns: ['SEARCHABLE_COLUMNS'] // Thay bằng các column có thể search
                },
                order: finalOrder,
                start: isNaN(parseInt(req.body.start)) ? 0 : parseInt(req.body.start),
                length: isNaN(parseInt(req.body.length)) ? 15 : parseInt(req.body.length),
                draw: req.body.draw || 1
            };

            commonService.getDataTableData(parameter).then(async responseData => {
                // Xử lý dữ liệu trước khi trả về (nếu cần)
                if(responseData.data && responseData.data.length > 0) {
                    responseData.data = responseData.data.map(item => {
                        // Thêm logic xử lý dữ liệu ở đây
                        return item;
                    });
                }
                res.json(responseData);
            });
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json({
                "data": [],
                "error": "Có lỗi xảy ra, vui lòng thử lại sau!",
                "draw": "1",
                "recordsFiltered": 0,
                "recordsTotal": 0
            });
        }
    }
};

/*
HƯỚNG DẪN SỬ DỤNG TEMPLATE:

1. Thay thế các placeholder:
   - TABLE_NAME: Tên bảng trong database
   - COLUMN_NAME_1, COLUMN_NAME_2, etc.: Tên các column thực tế
   - COLUMN_LIST: Danh sách columns cần select
   - SEARCHABLE_COLUMNS: Các column có thể search
   - PRIORITY_COLUMN, SECONDARY_COLUMN: Columns cho order mặc định

2. Tùy chỉnh logic kiểm tra quyền truy cập

3. Thêm xử lý dữ liệu đặc biệt (nếu cần) trong phần map responseData

4. Cập nhật frontend view tương ứng với cấu hình orderable/searchable

VÍ DỤ CỤ THỂ:
- TABLE_NAME: 'patients'
- COLUMN_NAME_1: 'fullname'
- COLUMN_NAME_2: 'phone'
- PRIORITY_COLUMN: 'khan_cap'
- SEARCHABLE_COLUMNS: ['fullname', 'phone', 'ma_benh_an']
*/
