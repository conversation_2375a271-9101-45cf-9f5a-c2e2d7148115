<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="<%= surveyConfig.description || 'Khảo sát trực tuyến' %>">
    <meta name="author" content="">

    <title><%= surveyConfig.name %> - <%= project.name %></title>

    <!-- Custom fonts for this template -->
    <link href="/vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i" rel="stylesheet">

    <!-- Custom styles for this template -->
    <link href="/css/sb-admin-2.css" rel="stylesheet">
    <link href="/css/survey-system.css" rel="stylesheet">

    <!-- Virtual Select CSS -->
    <link href="/vendor/virtual-select/virtual-select.min.css" rel="stylesheet">

    <!-- Flatpickr CSS -->
    <link href="/vendor/flatpickr/flatpickr.min.css" rel="stylesheet">
</head>

<body class="bg-gradient-primary">

    <div class="container">
        <div class="survey-form-container">
            <!-- Survey Header -->
            <div class="survey-header">
                <h1><%= surveyConfig.name %></h1>
                <% if (surveyConfig.description) { %>
                <p><%= surveyConfig.description %></p>
                <% } %>
                <small>Dự án: <%= project.name %></small>
            </div>

            <!-- Survey Body -->
            <div class="survey-body">
                <form id="public-survey-form">
                    <% surveyFields.forEach(function(field, index) { %>
                    <div class="survey-field">
                        <label for="<%= field.field_name %>" class="<%= field.is_required ? 'required' : '' %>">
                            <%= field.field_label %>
                        </label>

                        <% if (field.field_type === 'text') { %>
                            <input type="text" 
                                   id="<%= field.field_name %>" 
                                   name="<%= field.field_name %>" 
                                   class="form-control"
                                   placeholder="<%= field.placeholder || '' %>"
                                   <%= field.is_required ? 'required' : '' %>>

                        <% } else if (field.field_type === 'textarea') { %>
                            <textarea id="<%= field.field_name %>" 
                                      name="<%= field.field_name %>" 
                                      class="form-control"
                                      placeholder="<%= field.placeholder || '' %>"
                                      <%= field.is_required ? 'required' : '' %>></textarea>

                        <% } else if (field.field_type === 'email') { %>
                            <input type="email" 
                                   id="<%= field.field_name %>" 
                                   name="<%= field.field_name %>" 
                                   class="form-control"
                                   placeholder="<%= field.placeholder || '<EMAIL>' %>"
                                   <%= field.is_required ? 'required' : '' %>>

                        <% } else if (field.field_type === 'number') { %>
                            <input type="number" 
                                   id="<%= field.field_name %>" 
                                   name="<%= field.field_name %>" 
                                   class="form-control"
                                   placeholder="<%= field.placeholder || '' %>"
                                   <%= field.is_required ? 'required' : '' %>>

                        <% } else if (field.field_type === 'select') { %>
                            <select id="<%= field.field_name %>" 
                                    name="<%= field.field_name %>" 
                                    class="form-control"
                                    <%= field.is_required ? 'required' : '' %>>
                                <option value="">-- Chọn --</option>
                                <% if (field.field_options && Array.isArray(field.field_options)) { %>
                                    <% field.field_options.forEach(function(option) { %>
                                    <option value="<%= option.value %>"><%= option.label %></option>
                                    <% }); %>
                                <% } %>
                            </select>

                        <% } else if (field.field_type === 'multiselect') { %>
                            <select id="<%= field.field_name %>" 
                                    name="<%= field.field_name %>" 
                                    class="virtual-select multiple"
                                    multiple
                                    data-placeholder="<%= field.placeholder || 'Chọn nhiều tùy chọn...' %>"
                                    <%= field.is_required ? 'required' : '' %>>
                                <% if (field.field_options && Array.isArray(field.field_options)) { %>
                                    <% field.field_options.forEach(function(option) { %>
                                    <option value="<%= option.value %>"><%= option.label %></option>
                                    <% }); %>
                                <% } %>
                            </select>

                        <% } else if (field.field_type === 'radio') { %>
                            <div class="radio-group">
                                <% if (field.field_options && Array.isArray(field.field_options)) { %>
                                    <% field.field_options.forEach(function(option, optIndex) { %>
                                    <div class="radio-option">
                                        <input type="radio" 
                                               id="<%= field.field_name %>_<%= optIndex %>" 
                                               name="<%= field.field_name %>" 
                                               value="<%= option.value %>"
                                               <%= field.is_required ? 'required' : '' %>>
                                        <label for="<%= field.field_name %>_<%= optIndex %>"><%= option.label %></label>
                                    </div>
                                    <% }); %>
                                <% } %>
                            </div>

                        <% } else if (field.field_type === 'checkbox') { %>
                            <div class="checkbox-group">
                                <% if (field.field_options && Array.isArray(field.field_options)) { %>
                                    <% field.field_options.forEach(function(option, optIndex) { %>
                                    <div class="checkbox-option">
                                        <input type="checkbox" 
                                               id="<%= field.field_name %>_<%= optIndex %>" 
                                               name="<%= field.field_name %>" 
                                               value="<%= option.value %>">
                                        <label for="<%= field.field_name %>_<%= optIndex %>"><%= option.label %></label>
                                    </div>
                                    <% }); %>
                                <% } %>
                            </div>

                        <% } else if (field.field_type === 'date') { %>
                            <input type="text" 
                                   id="<%= field.field_name %>" 
                                   name="<%= field.field_name %>" 
                                   class="form-control flatpickr-date"
                                   placeholder="<%= field.placeholder || 'Chọn ngày...' %>"
                                   <%= field.is_required ? 'required' : '' %>>

                        <% } else if (field.field_type === 'datetime') { %>
                            <input type="text" 
                                   id="<%= field.field_name %>" 
                                   name="<%= field.field_name %>" 
                                   class="form-control flatpickr-datetime"
                                   placeholder="<%= field.placeholder || 'Chọn ngày và giờ...' %>"
                                   <%= field.is_required ? 'required' : '' %>>

                        <% } %>

                        <% if (field.help_text) { %>
                        <div class="help-text">
                            <%= field.help_text %>
                        </div>
                        <% } %>
                    </div>
                    <% }); %>

                    <% if (surveyConfig.require_email && !surveyFields.some(f => f.field_type === 'email')) { %>
                    <div class="survey-field">
                        <label for="respondent_email" class="required">Email của bạn</label>
                        <input type="email" 
                               id="respondent_email" 
                               name="respondent_email" 
                               class="form-control"
                               placeholder="<EMAIL>"
                               required>
                        <div class="help-text">
                            Email này sẽ được sử dụng để liên hệ nếu cần thiết.
                        </div>
                    </div>
                    <% } %>

                    <div class="text-center mt-4">
                        <button type="submit" class="survey-submit-btn">
                            <i class="fas fa-paper-plane"></i> Gửi Khảo sát
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="/vendor/jquery/jquery.min.js"></script>
    <script src="/js/bootstrap.bundle.min.js"></script>

    <!-- SweetAlert2 -->
    <script src="/js/<EMAIL>"></script>

    <!-- Virtual Select JS -->
    <script src="/vendor/virtual-select/virtual-select.min.js"></script>

    <!-- Flatpickr JS -->
    <script src="/vendor/flatpickr/flatpickr.min.js"></script>
    <script src="/vendor/flatpickr/l10n/vn.js"></script>

    <!-- Survey System JS -->
    <script src="/js/survey-system.js"></script>

    <script>
        $(document).ready(function() {
            // Initialize public survey form
            if (typeof initializePublicSurveyForm === 'function') {
                initializePublicSurveyForm();
            }
        });
    </script>

</body>

</html>
