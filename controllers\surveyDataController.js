const moment = require('moment');
const commonService = require('../services/commonService');
const securityService = require('../services/securityService');
const sqliteService = require('../services/sqliteService');

const surveyDataController = {
    /**
     * <PERSON><PERSON>n thị danh sách dữ liệu kh<PERSON>o sát từ SQLite
     */
    getList: async (req, res) => {
        try {
            const user = req.user;
            const projectId = req.params.projectId;
            const errors = [];
            
            // Kiểm tra dự án tồn tại và quyền truy cập
            const projectResponse = await commonService.getAllDataTable('projects', {
                id: projectId,
                active: [0, 1]
            });
            
            if (!projectResponse.success || !projectResponse.data || projectResponse.data.length === 0) {
                return res.render("error", {
                    user: user,
                    message: "<PERSON>hông tìm thấy dự án",
                    status: 404
                });
            }
            
            const project = projectResponse.data[0];
            if (!securityService.canAccessRecord(user, project)) {
                return res.render("error", {
                    user: user,
                    message: "Bạn không có quyền truy cập dự án này",
                    status: 403
                });
            }
            
            // Kiểm tra file SQLite tồn tại
            const sqlitePath = project.sqlite_db_path || sqliteService.getProjectDatabasePath(projectId);
            if (!sqlitePath) {
                return res.render("error", {
                    user: user,
                    message: "Không tìm thấy database SQLite cho dự án này",
                    status: 404
                });
            }
            
            // Lấy thống kê
            let statistics = {};
            try {
                statistics = await sqliteService.getStatistics(sqlitePath);
            } catch (error) {
                console.warn('Could not get statistics:', error.message);
            }
            
            res.render('survey-data/index', {
                user: user,
                errors: errors,
                project: project,
                statistics: statistics,
                moment: moment
            });
        } catch (error) {
            console.error('Error in surveyDataController.getList:', error.message);
            return res.render("error", {
                user: req.user,
                message: "Có lỗi xảy ra khi tải trang",
                status: 500
            });
        }
    },

    /**
     * API lấy danh sách dữ liệu khảo sát cho DataTable
     */
    getListTable: async (req, res) => {
        try {
            const user = req.user;
            const projectId = req.params.projectId;
            
            // Kiểm tra dự án và quyền truy cập
            const projectResponse = await commonService.getAllDataTable('projects', {
                id: projectId,
                active: [0, 1]
            });
            
            if (!projectResponse.success || !projectResponse.data || projectResponse.data.length === 0) {
                return res.json({
                    draw: req.body.draw || 1,
                    recordsTotal: 0,
                    recordsFiltered: 0,
                    data: [],
                    error: "Không tìm thấy dự án"
                });
            }
            
            const project = projectResponse.data[0];
            if (!securityService.canAccessRecord(user, project)) {
                return res.json({
                    draw: req.body.draw || 1,
                    recordsTotal: 0,
                    recordsFiltered: 0,
                    data: [],
                    error: "Bạn không có quyền truy cập dự án này"
                });
            }
            
            // Lấy file SQLite
            const sqlitePath = project.sqlite_db_path || sqliteService.getProjectDatabasePath(projectId);
            if (!sqlitePath) {
                return res.json({
                    draw: req.body.draw || 1,
                    recordsTotal: 0,
                    recordsFiltered: 0,
                    data: [],
                    error: "Không tìm thấy database SQLite"
                });
            }
            
            // Chuẩn bị filters
            const filters = {};
            
            if (req.body.search && req.body.search.value) {
                filters.email = req.body.search.value;
            }
            
            if (req.body.length) {
                filters.limit = parseInt(req.body.length);
            }
            
            // Lấy dữ liệu từ SQLite
            const responses = await sqliteService.getSurveyResponses(sqlitePath, filters);
            
            // Format dữ liệu cho DataTable
            const formattedData = responses.map(response => {
                return {
                    id: response.id,
                    respondent_email: response.respondent_email || '',
                    respondent_ip: response.respondent_ip || '',
                    submitted_at: moment(response.submitted_at).format('DD/MM/YYYY HH:mm:ss'),
                    response_data: response.response_data || '',
                    actions: `
                        <button class="btn btn-sm btn-info view-response" data-id="${response.id}">
                            <i class="fas fa-eye"></i> Xem
                        </button>
                        <button class="btn btn-sm btn-warning edit-response" data-id="${response.id}">
                            <i class="fas fa-edit"></i> Sửa
                        </button>
                        <button class="btn btn-sm btn-danger delete-response" data-id="${response.id}">
                            <i class="fas fa-trash"></i> Xóa
                        </button>
                    `
                };
            });
            
            res.json({
                draw: req.body.draw || 1,
                recordsTotal: responses.length,
                recordsFiltered: responses.length,
                data: formattedData
            });
            
        } catch (error) {
            console.error('Error in surveyDataController.getListTable:', error.message);
            res.json({
                draw: req.body.draw || 1,
                recordsTotal: 0,
                recordsFiltered: 0,
                data: [],
                error: "Có lỗi xảy ra khi tải dữ liệu"
            });
        }
    },

    /**
     * Lấy chi tiết response
     */
    getDetail: async (req, res) => {
        try {
            const user = req.user;
            const projectId = req.params.projectId;
            const responseId = req.params.responseId;
            
            // Kiểm tra dự án và quyền truy cập
            const projectResponse = await commonService.getAllDataTable('projects', {
                id: projectId,
                active: [0, 1]
            });
            
            if (!projectResponse.success || !projectResponse.data || projectResponse.data.length === 0) {
                return res.status(404).json({
                    success: false,
                    message: "Không tìm thấy dự án"
                });
            }
            
            const project = projectResponse.data[0];
            if (!securityService.canAccessRecord(user, project)) {
                return res.status(403).json({
                    success: false,
                    message: "Bạn không có quyền truy cập dự án này"
                });
            }
            
            // Lấy file SQLite
            const sqlitePath = project.sqlite_db_path || sqliteService.getProjectDatabasePath(projectId);
            if (!sqlitePath) {
                return res.status(404).json({
                    success: false,
                    message: "Không tìm thấy database SQLite"
                });
            }
            
            // Lấy chi tiết response
            const responseDetail = await sqliteService.getSurveyResponseDetail(sqlitePath, responseId);
            
            if (!responseDetail) {
                return res.status(404).json({
                    success: false,
                    message: "Không tìm thấy response"
                });
            }
            
            res.json({
                success: true,
                data: responseDetail
            });
            
        } catch (error) {
            console.error('Error in surveyDataController.getDetail:', error.message);
            res.status(500).json({
                success: false,
                message: "Có lỗi xảy ra khi tải chi tiết"
            });
        }
    },

    /**
     * Cập nhật response
     */
    update: async (req, res) => {
        const resultData = {
            success: false,
            message: '',
            data: null
        };

        try {
            const user = req.user;
            const projectId = req.params.projectId;
            const responseId = req.params.responseId;
            const updateData = req.body;
            
            // Kiểm tra dự án và quyền truy cập
            const projectResponse = await commonService.getAllDataTable('projects', {
                id: projectId,
                active: [0, 1]
            });
            
            if (!projectResponse.success || !projectResponse.data || projectResponse.data.length === 0) {
                resultData.message = 'Không tìm thấy dự án!';
                return res.json(resultData);
            }
            
            const project = projectResponse.data[0];
            if (!securityService.canAccessRecord(user, project)) {
                resultData.message = 'Bạn không có quyền truy cập dự án này!';
                return res.json(resultData);
            }
            
            // Lấy file SQLite
            const sqlitePath = project.sqlite_db_path || sqliteService.getProjectDatabasePath(projectId);
            if (!sqlitePath) {
                resultData.message = 'Không tìm thấy database SQLite!';
                return res.json(resultData);
            }
            
            // Cập nhật response
            await sqliteService.updateSurveyResponse(sqlitePath, responseId, updateData);
            
            resultData.success = true;
            resultData.message = 'Cập nhật thành công!';
            
            res.json(resultData);
            
        } catch (error) {
            console.error('Error in surveyDataController.update:', error.message);
            res.json(securityService.createErrorResponse("Có lỗi xảy ra khi cập nhật!"));
        }
    },

    /**
     * Xóa response
     */
    delete: async (req, res) => {
        const resultData = {
            success: false,
            message: '',
            data: null
        };

        try {
            const user = req.user;
            const projectId = req.params.projectId;
            const responseId = req.params.responseId;
            
            // Kiểm tra dự án và quyền truy cập
            const projectResponse = await commonService.getAllDataTable('projects', {
                id: projectId,
                active: [0, 1]
            });
            
            if (!projectResponse.success || !projectResponse.data || projectResponse.data.length === 0) {
                resultData.message = 'Không tìm thấy dự án!';
                return res.json(resultData);
            }
            
            const project = projectResponse.data[0];
            if (!securityService.canAccessRecord(user, project)) {
                resultData.message = 'Bạn không có quyền truy cập dự án này!';
                return res.json(resultData);
            }
            
            // Lấy file SQLite
            const sqlitePath = project.sqlite_db_path || sqliteService.getProjectDatabasePath(projectId);
            if (!sqlitePath) {
                resultData.message = 'Không tìm thấy database SQLite!';
                return res.json(resultData);
            }
            
            // Xóa response
            const deleted = await sqliteService.deleteSurveyResponse(sqlitePath, responseId);
            
            if (deleted) {
                resultData.success = true;
                resultData.message = 'Xóa thành công!';
            } else {
                resultData.message = 'Không tìm thấy response để xóa!';
            }
            
            res.json(resultData);

        } catch (error) {
            console.error('Error in surveyDataController.delete:', error.message);
            res.json(securityService.createErrorResponse("Có lỗi xảy ra khi xóa!"));
        }
    },

    /**
     * Xuất dữ liệu ra file Excel
     */
    exportExcel: async (req, res) => {
        try {
            const user = req.user;
            const projectId = req.params.projectId;

            // Kiểm tra dự án và quyền truy cập
            const projectResponse = await commonService.getAllDataTable('projects', {
                id: projectId,
                active: [0, 1]
            });

            if (!projectResponse.success || !projectResponse.data || projectResponse.data.length === 0) {
                return res.status(404).json({
                    success: false,
                    message: "Không tìm thấy dự án"
                });
            }

            const project = projectResponse.data[0];
            if (!securityService.canAccessRecord(user, project)) {
                return res.status(403).json({
                    success: false,
                    message: "Bạn không có quyền truy cập dự án này"
                });
            }

            // Lấy file SQLite
            const sqlitePath = project.sqlite_db_path || sqliteService.getProjectDatabasePath(projectId);
            if (!sqlitePath) {
                return res.status(404).json({
                    success: false,
                    message: "Không tìm thấy database SQLite"
                });
            }

            // Lấy survey fields để tạo headers
            const surveyConfigsResponse = await commonService.getAllDataTable('survey_configs', {
                project_id: projectId,
                active: 1
            });

            let surveyFields = [];
            if (surveyConfigsResponse.success && surveyConfigsResponse.data && surveyConfigsResponse.data.length > 0) {
                const surveyConfig = surveyConfigsResponse.data[0];
                const fieldsResponse = await commonService.getAllDataTable('survey_fields', {
                    survey_config_id: surveyConfig.id,
                    active: 1
                });

                if (fieldsResponse.success && fieldsResponse.data) {
                    surveyFields = fieldsResponse.data.sort((a, b) => a.field_order - b.field_order);
                }
            }

            // Chuẩn bị filters từ query params
            const filters = {};
            if (req.query.email) filters.email = req.query.email;
            if (req.query.date_from) filters.date_from = req.query.date_from;
            if (req.query.date_to) filters.date_to = req.query.date_to;

            // Xuất Excel
            const excelBuffer = await sqliteService.exportToExcel(sqlitePath, surveyFields, filters);

            // Tạo tên file
            const timestamp = moment().format('YYYY-MM-DD_HH-mm-ss');
            const fileName = `${project.name.replace(/[^a-zA-Z0-9]/g, '_')}_survey_data_${timestamp}.xlsx`;

            // Set headers và gửi file
            res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
            res.setHeader('Content-Length', excelBuffer.length);

            res.send(excelBuffer);

        } catch (error) {
            console.error('Error in surveyDataController.exportExcel:', error.message);
            res.status(500).json({
                success: false,
                message: "Có lỗi xảy ra khi xuất file Excel"
            });
        }
    }
};

module.exports = surveyDataController;
